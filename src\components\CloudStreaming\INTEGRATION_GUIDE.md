# LarkSR Control Panel Integration Guide

This guide shows you how to integrate the LarkSR control panel and settings into your existing CloudStreamingView component.

## Quick Integration Steps

### Step 1: Add the Control Panel Components

Copy these files to your project:
- `LarkSRControlPanel.jsx`
- `LarkSRControlPanel.css`
- `LarkSRToolbar.jsx`
- `LarkSRToolbar.css`

### Step 2: Update Your Existing CloudStreamingView

Add these imports to your existing `CloudStreamingView.jsx`:

```jsx
import LarkSRControlPanel from './LarkSRControlPanel';
import LarkSRToolbar from './LarkSRToolbar';
```

### Step 3: Add Settings State

Add settings state to your component's constructor:

```jsx
constructor(props) {
  super(props);
  this.state = {
    // ... your existing state
    showControlPanel: false,
    larkSRSettings: {
      scaleMode: 'contain',
      fullScreenMode: 0,
      mobileFullScreenMode: 1,
      audioEnabled: true,
      volume: 1.0,
      logLevel: 'warn',
      useSeparateMediaSharePeer: true,
      joystickEnabled: true,
      keyboardEnabled: true,
      joystickSubType: 1,
      showNetworkStatus: true,
      showControls: true,
      reconnectAttempts: 3,
      connectionTimeout: 30000,
    }
  };
}
```

### Step 4: Load Settings in componentDidMount

Add this method and call it in componentDidMount:

```jsx
loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem('larksr_settings');
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings);
      this.setState({
        larkSRSettings: { ...this.state.larkSRSettings, ...parsedSettings }
      });
    }
  } catch (error) {
    console.warn('Failed to load LarkSR settings:', error);
  }
};

componentDidMount() {
  this.loadSettings(); // Add this line
  this.initializeLarkSR();
}
```

### Step 5: Update LarkSR Initialization

Modify your LarkSR initialization to use settings:

```jsx
// In your initializeLarkSR method, replace the LarkSR constructor with:
const larksr = new LarkSR({
  rootElement: this.myRef.current,
  serverAddress: "https://ps.propvr.io:8181/",
  scaleMode: this.state.larkSRSettings.scaleMode,
  fullScreenMode: this.state.larkSRSettings.fullScreenMode,
  mobileFullScreenMode: this.state.larkSRSettings.mobileFullScreenMode,
  logLevel: this.state.larkSRSettings.logLevel,
  useSeparateMediaSharePeer: this.state.larkSRSettings.useSeparateMediaSharePeer,
});
```

### Step 6: Add Event Handlers

Add these methods to your component:

```jsx
// Settings handlers
handleSettingsChange = (newSettings) => {
  console.log("Settings changed:", newSettings);
  this.setState({ larkSRSettings: newSettings });
  
  // Save to localStorage
  try {
    localStorage.setItem('larksr_settings', JSON.stringify(newSettings));
  } catch (error) {
    console.warn('Failed to save settings:', error);
  }
  
  // Check if reconnection is needed
  const reconnectRequired = [
    'scaleMode', 'fullScreenMode', 'mobileFullScreenMode', 
    'logLevel', 'useSeparateMediaSharePeer'
  ];
  
  const needsReconnect = reconnectRequired.some(key => 
    this.state.larkSRSettings[key] !== newSettings[key]
  );
  
  if (needsReconnect && this.state.sdkConnected) {
    console.log("Settings require reconnection, reinitializing...");
    this.reinitializeLarkSR();
  }
};

handleOpenSettings = () => {
  this.setState({ showControlPanel: true });
};

handleCloseSettings = () => {
  this.setState({ showControlPanel: false });
};

// Toolbar handlers
handleFullscreen = () => {
  if (this.larksr && this.larksr.fullScreen) {
    this.larksr.fullScreen.requestFullScreen();
  } else if (this.myRef.current) {
    if (this.myRef.current.requestFullscreen) {
      this.myRef.current.requestFullscreen();
    }
  }
};

handleScreenshot = () => {
  if (this.larksr && this.larksr.screenshot) {
    this.larksr.screenshot()
      .then((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `larksr-screenshot-${Date.now()}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      })
      .catch((error) => {
        console.error("Screenshot failed:", error);
      });
  }
};

handleDisconnect = () => {
  if (this.larksr) {
    this.cleanup();
    this.setState({
      sdkConnected: false,
      remoteReady: false,
      connectionError: null,
      isConnecting: false
    });
  }
};

handleReconnect = () => {
  this.setState({ isConnecting: true, connectionError: null });
  this.initializeLarkSR();
};

getConnectionStatus = () => {
  if (this.state.isConnecting) return 'connecting';
  if (this.state.connectionError) return 'error';
  if (this.state.sdkConnected) return 'connected';
  return 'disconnected';
};
```

### Step 7: Update Render Method

Add the control panel and toolbar to your render method:

```jsx
render() {
  return (
    <div ref={this.myRef} style={{ position: 'relative', width: '100%', height: '100%' }}>
      {/* Your existing UI containers */}
      <div ref={this.uiContainerRef} style={{ /* your existing styles */ }} />
      <div ref={this.uiKeyboardRef} style={{ /* your existing styles */ }} />

      {/* Add Control Panel */}
      {this.state.showControlPanel && (
        <LarkSRControlPanel
          onSettingsChange={this.handleSettingsChange}
          onClose={this.handleCloseSettings}
          initialSettings={this.state.larkSRSettings}
        />
      )}

      {/* Add Toolbar */}
      {this.state.larkSRSettings.showControls && (
        <LarkSRToolbar
          larksr={this.larksr}
          connectionStatus={this.getConnectionStatus()}
          showNetworkStats={this.state.larkSRSettings.showNetworkStatus}
          onOpenSettings={this.handleOpenSettings}
          onFullscreen={this.handleFullscreen}
          onScreenshot={this.handleScreenshot}
          onDisconnect={this.handleDisconnect}
          onReconnect={this.handleReconnect}
        />
      )}

      {/* Your existing loading and error overlays */}
      {/* ... */}
    </div>
  );
}
```

## Minimal Integration (Just Settings Button)

If you want to add just a settings button without the full toolbar:

```jsx
// Add this to your render method
<button
  onClick={this.handleOpenSettings}
  style={{
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 2000,
    background: 'rgba(0,0,0,0.7)',
    color: 'white',
    border: 'none',
    borderRadius: '50%',
    width: 40,
    height: 40,
    cursor: 'pointer'
  }}
>
  ⚙️
</button>
```

## Testing the Integration

1. **Settings Persistence**: Change settings, refresh the page, verify settings are remembered
2. **Mobile Controls**: Test on mobile device or browser dev tools mobile mode
3. **Connection Status**: Test disconnect/reconnect functionality
4. **Fullscreen**: Test fullscreen toggle
5. **Screenshot**: Test screenshot functionality (if supported by your LarkSR version)

## Customization Options

### Custom Styling
- Modify the CSS files to match your app's theme
- Add custom CSS classes to override default styles

### Custom Settings
- Add new settings to the `larkSRSettings` object
- Add corresponding UI controls in `LarkSRControlPanel.jsx`
- Handle the new settings in your LarkSR initialization

### Custom Toolbar Buttons
- Add new buttons to `LarkSRToolbar.jsx`
- Implement corresponding event handlers
- Pass handlers as props to the toolbar component

## Troubleshooting

### Settings Not Saving
- Check browser localStorage permissions
- Verify JSON serialization of settings object

### Mobile Controls Not Working
- Ensure `PxyWebCommonUI` is properly imported
- Check `Capabilities.isMobile` detection
- Verify mobile control initialization in settings

### Toolbar Not Showing
- Check `showControls` setting is enabled
- Verify toolbar CSS is imported
- Check z-index conflicts with other elements

This integration should give you a fully functional control panel and settings system for your LarkSR implementation!
