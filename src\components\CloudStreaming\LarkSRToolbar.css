/* LarkSR Toolbar Styles */
.larksr-toolbar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
}

.larksr-toolbar.collapsed {
  transform: translateY(calc(100% - 40px));
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  min-height: 40px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Connection Status */
.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-text {
  white-space: nowrap;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 currentColor;
  }
  70% {
    box-shadow: 0 0 0 10px transparent;
  }
  100% {
    box-shadow: 0 0 0 0 transparent;
  }
}

/* Network Stats */
.network-stats {
  display: flex;
  gap: 15px;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 12px;
  color: white;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Control Buttons */
.control-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.toolbar-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.toolbar-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.toolbar-button:active {
  transform: translateY(0);
}

.toolbar-button.disconnect {
  background: rgba(220, 53, 69, 0.2);
  border-color: rgba(220, 53, 69, 0.4);
}

.toolbar-button.disconnect:hover {
  background: rgba(220, 53, 69, 0.3);
  border-color: rgba(220, 53, 69, 0.6);
}

.toolbar-button.reconnect {
  background: rgba(40, 167, 69, 0.2);
  border-color: rgba(40, 167, 69, 0.4);
}

.toolbar-button.reconnect:hover {
  background: rgba(40, 167, 69, 0.3);
  border-color: rgba(40, 167, 69, 0.6);
}

.toolbar-button.settings {
  background: rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.4);
}

.toolbar-button.settings:hover {
  background: rgba(102, 126, 234, 0.3);
  border-color: rgba(102, 126, 234, 0.6);
}

.toolbar-button.toggle {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.toolbar-button.toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .toolbar-content {
    padding: 8px 10px;
    flex-direction: column;
    gap: 10px;
  }
  
  .toolbar-left {
    width: 100%;
    justify-content: center;
    gap: 15px;
  }
  
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .network-stats {
    gap: 10px;
  }
  
  .stat-item {
    gap: 1px;
  }
  
  .stat-label {
    font-size: 9px;
  }
  
  .stat-value {
    font-size: 11px;
  }
  
  .connection-status {
    font-size: 13px;
  }
  
  .toolbar-button {
    min-width: 36px;
    height: 36px;
    padding: 10px;
  }
  
  .larksr-toolbar.collapsed {
    transform: translateY(calc(100% - 50px));
  }
}

@media (max-width: 480px) {
  .network-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    width: 100%;
  }
  
  .control-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Accessibility */
.toolbar-button:focus {
  outline: 2px solid rgba(102, 126, 234, 0.8);
  outline-offset: 2px;
}

/* Animations */
.larksr-toolbar {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Tooltip styles */
.toolbar-button[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1001;
  margin-bottom: 5px;
}

.toolbar-button[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  z-index: 1001;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .larksr-toolbar {
    background: black;
    border-top: 2px solid white;
  }
  
  .toolbar-button {
    border: 2px solid white;
  }
  
  .status-indicator {
    border: 1px solid white;
  }
}
