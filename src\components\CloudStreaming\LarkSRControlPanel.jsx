import React, { Component } from 'react';
import './LarkSRControlPanel.css';

export default class LarkSRControlPanel extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVisible: false,
      settings: {
        // Video Settings
        scaleMode: 'contain', // contain, cover, fill, none
        fullScreenMode: 0, // 0: manual, 1: first click, 2: every click
        mobileFullScreenMode: 1,
        
        // Audio Settings
        audioEnabled: true,
        volume: 1.0,
        
        // Network Settings
        logLevel: 'warn', // debug, info, warn, error
        useSeparateMediaSharePeer: true,
        
        // Input Settings
        joystickEnabled: true,
        keyboardEnabled: true,
        joystickSubType: 1, // 1: WASD, 2: arrows, 3: joystick
        
        // Display Settings
        showNetworkStatus: true,
        showControls: true,
        
        // Advanced Settings
        reconnectAttempts: 3,
        connectionTimeout: 30000,
      }
    };
  }

  componentDidMount() {
    // Load saved settings from localStorage
    this.loadSettings();
  }

  loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('larksr_settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        this.setState({
          settings: { ...this.state.settings, ...parsedSettings }
        });
      }
    } catch (error) {
      console.warn('Failed to load LarkSR settings:', error);
    }
  };

  saveSettings = () => {
    try {
      localStorage.setItem('larksr_settings', JSON.stringify(this.state.settings));
      if (this.props.onSettingsChange) {
        this.props.onSettingsChange(this.state.settings);
      }
    } catch (error) {
      console.warn('Failed to save LarkSR settings:', error);
    }
  };

  updateSetting = (key, value) => {
    this.setState(
      {
        settings: {
          ...this.state.settings,
          [key]: value
        }
      },
      () => {
        this.saveSettings();
      }
    );
  };

  togglePanel = () => {
    this.setState({ isVisible: !this.state.isVisible });
  };

  resetSettings = () => {
    if (window.confirm('Are you sure you want to reset all settings to default?')) {
      localStorage.removeItem('larksr_settings');
      this.setState({
        settings: {
          scaleMode: 'contain',
          fullScreenMode: 0,
          mobileFullScreenMode: 1,
          audioEnabled: true,
          volume: 1.0,
          logLevel: 'warn',
          useSeparateMediaSharePeer: true,
          joystickEnabled: true,
          keyboardEnabled: true,
          joystickSubType: 1,
          showNetworkStatus: true,
          showControls: true,
          reconnectAttempts: 3,
          connectionTimeout: 30000,
        }
      }, () => {
        this.saveSettings();
      });
    }
  };

  renderVideoSettings = () => (
    <div className="settings-section">
      <h3>Video Settings</h3>
      
      <div className="setting-item">
        <label>Scale Mode:</label>
        <select 
          value={this.state.settings.scaleMode}
          onChange={(e) => this.updateSetting('scaleMode', e.target.value)}
        >
          <option value="contain">Contain (Keep aspect ratio)</option>
          <option value="cover">Cover (Fill container)</option>
          <option value="fill">Fill (Stretch to fit)</option>
          <option value="none">None (Original size)</option>
        </select>
      </div>

      <div className="setting-item">
        <label>Fullscreen Mode:</label>
        <select 
          value={this.state.settings.fullScreenMode}
          onChange={(e) => this.updateSetting('fullScreenMode', parseInt(e.target.value))}
        >
          <option value={0}>Manual trigger</option>
          <option value={1}>First click trigger</option>
          <option value={2}>Every click trigger</option>
        </select>
      </div>

      <div className="setting-item">
        <label>Mobile Fullscreen Mode:</label>
        <select 
          value={this.state.settings.mobileFullScreenMode}
          onChange={(e) => this.updateSetting('mobileFullScreenMode', parseInt(e.target.value))}
        >
          <option value={0}>Manual trigger</option>
          <option value={1}>First click trigger</option>
          <option value={2}>Every click trigger</option>
        </select>
      </div>
    </div>
  );

  renderAudioSettings = () => (
    <div className="settings-section">
      <h3>Audio Settings</h3>
      
      <div className="setting-item">
        <label>
          <input
            type="checkbox"
            checked={this.state.settings.audioEnabled}
            onChange={(e) => this.updateSetting('audioEnabled', e.target.checked)}
          />
          Enable Audio
        </label>
      </div>

      <div className="setting-item">
        <label>Volume:</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={this.state.settings.volume}
          onChange={(e) => this.updateSetting('volume', parseFloat(e.target.value))}
        />
        <span>{Math.round(this.state.settings.volume * 100)}%</span>
      </div>
    </div>
  );

  renderNetworkSettings = () => (
    <div className="settings-section">
      <h3>Network Settings</h3>
      
      <div className="setting-item">
        <label>Log Level:</label>
        <select 
          value={this.state.settings.logLevel}
          onChange={(e) => this.updateSetting('logLevel', e.target.value)}
        >
          <option value="debug">Debug</option>
          <option value="info">Info</option>
          <option value="warn">Warning</option>
          <option value="error">Error</option>
        </select>
      </div>

      <div className="setting-item">
        <label>
          <input
            type="checkbox"
            checked={this.state.settings.useSeparateMediaSharePeer}
            onChange={(e) => this.updateSetting('useSeparateMediaSharePeer', e.target.checked)}
          />
          Use Separate Media Share Peer
        </label>
      </div>

      <div className="setting-item">
        <label>Connection Timeout (ms):</label>
        <input
          type="number"
          min="5000"
          max="60000"
          step="1000"
          value={this.state.settings.connectionTimeout}
          onChange={(e) => this.updateSetting('connectionTimeout', parseInt(e.target.value))}
        />
      </div>

      <div className="setting-item">
        <label>Reconnect Attempts:</label>
        <input
          type="number"
          min="0"
          max="10"
          value={this.state.settings.reconnectAttempts}
          onChange={(e) => this.updateSetting('reconnectAttempts', parseInt(e.target.value))}
        />
      </div>
    </div>
  );

  renderInputSettings = () => (
    <div className="settings-section">
      <h3>Input Settings</h3>
      
      <div className="setting-item">
        <label>
          <input
            type="checkbox"
            checked={this.state.settings.joystickEnabled}
            onChange={(e) => this.updateSetting('joystickEnabled', e.target.checked)}
          />
          Enable Joystick (Mobile)
        </label>
      </div>

      <div className="setting-item">
        <label>Joystick Type:</label>
        <select 
          value={this.state.settings.joystickSubType}
          onChange={(e) => this.updateSetting('joystickSubType', parseInt(e.target.value))}
          disabled={!this.state.settings.joystickEnabled}
        >
          <option value={1}>WASD Keys</option>
          <option value={2}>Arrow Keys</option>
          <option value={3}>Physical Joystick</option>
          <option value={0}>None (Manual handling)</option>
        </select>
      </div>

      <div className="setting-item">
        <label>
          <input
            type="checkbox"
            checked={this.state.settings.keyboardEnabled}
            onChange={(e) => this.updateSetting('keyboardEnabled', e.target.checked)}
          />
          Enable Virtual Keyboard (Mobile)
        </label>
      </div>
    </div>
  );

  renderDisplaySettings = () => (
    <div className="settings-section">
      <h3>Display Settings</h3>
      
      <div className="setting-item">
        <label>
          <input
            type="checkbox"
            checked={this.state.settings.showNetworkStatus}
            onChange={(e) => this.updateSetting('showNetworkStatus', e.target.checked)}
          />
          Show Network Status
        </label>
      </div>

      <div className="setting-item">
        <label>
          <input
            type="checkbox"
            checked={this.state.settings.showControls}
            onChange={(e) => this.updateSetting('showControls', e.target.checked)}
          />
          Show Control Panel
        </label>
      </div>
    </div>
  );

  render() {
    if (!this.state.isVisible) {
      return null;
    }

    return (
      <div className="larksr-control-panel-overlay">
        <div className="larksr-control-panel">
          <div className="panel-header">
            <h2>LarkSR Settings</h2>
            <button 
              className="close-button"
              onClick={this.togglePanel}
              aria-label="Close settings"
            >
              ×
            </button>
          </div>
          
          <div className="panel-content">
            {this.renderVideoSettings()}
            {this.renderAudioSettings()}
            {this.renderNetworkSettings()}
            {this.renderInputSettings()}
            {this.renderDisplaySettings()}
          </div>
          
          <div className="panel-footer">
            <button 
              className="reset-button"
              onClick={this.resetSettings}
            >
              Reset to Defaults
            </button>
            <button 
              className="close-button-footer"
              onClick={this.togglePanel}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }
}
