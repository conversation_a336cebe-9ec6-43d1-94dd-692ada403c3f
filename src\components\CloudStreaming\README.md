# LarkSR Enhanced Cloud Streaming Components

This directory contains enhanced components for integrating LarkSR SDK with comprehensive control panel and settings management.

## Components

### 1. CloudStreamingViewEnhanced.jsx
Enhanced version of your original CloudStreamingView component with:
- Integrated control panel and toolbar
- Persistent settings management
- Real-time configuration updates
- Mobile-responsive design
- Network monitoring
- Advanced error handling

### 2. LarkSRControlPanel.jsx
Comprehensive settings panel with:
- Video settings (scale mode, fullscreen options)
- Audio controls (volume, enable/disable)
- Network settings (log level, connection options)
- Input controls (joystick, keyboard settings)
- Display options (show/hide UI elements)
- Settings persistence in localStorage

### 3. LarkSRToolbar.jsx
Bottom toolbar with:
- Connection status indicator
- Real-time network statistics
- Quick action buttons (fullscreen, screenshot, disconnect)
- Collapsible design
- Mobile-responsive layout

## Usage

### Basic Integration

Replace your existing CloudStreamingView with the enhanced version:

```jsx
import CloudStreamingViewEnhanced from './components/CloudStreaming/CloudStreamingViewEnhanced';

function App() {
  return (
    <CloudStreamingViewEnhanced
      authCode="your-auth-code"
      applicationId="your-application-id"
      onConnectionSuccess={(sdkId) => {
        console.log('Connected with SDK ID:', sdkId);
      }}
      onConnectionError={(error) => {
        console.error('Connection failed:', error);
      }}
    />
  );
}
```

### Advanced Configuration

The component automatically loads and saves settings to localStorage. You can also programmatically access the settings:

```jsx
// Access the component reference
const streamingRef = useRef();

// Get current settings
const currentSettings = streamingRef.current?.state.larkSRSettings;

// Programmatically open settings panel
streamingRef.current?.handleOpenSettings();
```

## Settings Configuration

### Video Settings
- **Scale Mode**: How video is scaled in the container
  - `contain`: Keep aspect ratio, fit within container
  - `cover`: Fill container, may crop video
  - `fill`: Stretch to fill container
  - `none`: Original size

- **Fullscreen Mode**: When fullscreen is triggered
  - `0`: Manual trigger only
  - `1`: First click triggers fullscreen
  - `2`: Every click triggers fullscreen

### Audio Settings
- **Audio Enabled**: Enable/disable audio
- **Volume**: Audio volume (0.0 to 1.0)

### Network Settings
- **Log Level**: Console logging level (`debug`, `info`, `warn`, `error`)
- **Use Separate Media Share Peer**: Advanced WebRTC option
- **Connection Timeout**: Timeout for connection attempts (ms)
- **Reconnect Attempts**: Number of automatic reconnection attempts

### Input Settings
- **Joystick Enabled**: Enable virtual joystick on mobile
- **Joystick Type**: Input mapping for joystick
  - `1`: WASD keys
  - `2`: Arrow keys
  - `3`: Physical joystick
  - `0`: No automatic mapping

- **Keyboard Enabled**: Enable virtual keyboard on mobile

### Display Settings
- **Show Network Status**: Display network statistics in toolbar
- **Show Controls**: Show/hide the control toolbar

## Styling

The components include comprehensive CSS with:
- Modern design with gradients and animations
- Mobile-responsive layouts
- Dark theme optimized for streaming
- Accessibility features (focus states, high contrast support)
- Smooth transitions and hover effects

## Customization

### Theming
Modify the CSS files to match your application's theme:
- `LarkSRControlPanel.css`: Settings panel styling
- `LarkSRToolbar.css`: Toolbar styling

### Adding Custom Settings
Extend the settings object in `CloudStreamingViewEnhanced.jsx`:

```jsx
// Add new setting to state
larkSRSettings: {
  // ... existing settings
  customSetting: 'defaultValue',
}

// Add UI control in LarkSRControlPanel.jsx
<div className="setting-item">
  <label>Custom Setting:</label>
  <input
    type="text"
    value={this.state.settings.customSetting}
    onChange={(e) => this.updateSetting('customSetting', e.target.value)}
  />
</div>
```

## Events

The enhanced component provides several event callbacks:

- `onConnectionSuccess(sdkId)`: Called when connection is established
- `onConnectionError(error)`: Called when connection fails
- `onSettingsChange(newSettings)`: Called when settings are updated

## Mobile Support

The components are fully mobile-responsive with:
- Touch-friendly controls
- Virtual joystick and keyboard
- Adaptive layouts for different screen sizes
- Gesture support for fullscreen

## Browser Compatibility

Supports all modern browsers with:
- WebRTC support
- ES6+ features
- CSS Grid and Flexbox
- Local Storage API

## Troubleshooting

### Common Issues

1. **Settings not persisting**: Check localStorage permissions
2. **Mobile controls not showing**: Verify `Capabilities.isMobile` detection
3. **Network stats not updating**: Ensure LarkSR instance is properly connected
4. **Fullscreen not working**: Check browser fullscreen API support

### Debug Mode

Enable debug logging by setting log level to 'debug' in the control panel or programmatically:

```jsx
// Enable debug mode
streamingRef.current?.setState({
  larkSRSettings: {
    ...streamingRef.current.state.larkSRSettings,
    logLevel: 'debug'
  }
});
```
