import React from "react";
import { LarkSR } from "larksr_websdk";
import PxyWebCommonUI from 'pxy_webcommonui';
import LarkSRControlPanel from './LarkSRControlPanel';
import LarkSRToolbar from './LarkSRToolbar';

const { Capabilities } = PxyWebCommonUI;

export default class CloudStreamingViewEnhanced extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      remoteReady: false,
      isConnecting: true,
      connectionError: null,
      sdkConnected: false,
      showControlPanel: false,
      larkSRSettings: {
        scaleMode: 'contain',
        fullScreenMode: 0,
        mobileFullScreenMode: 1,
        audioEnabled: true,
        volume: 1.0,
        logLevel: 'warn',
        useSeparateMediaSharePeer: true,
        joystickEnabled: true,
        keyboardEnabled: true,
        joystickSubType: 1,
        showNetworkStatus: true,
        showControls: true,
        reconnectAttempts: 3,
        connectionTimeout: 30000,
      }
    }
    this.myRef = React.createRef();
    this.uiContainerRef = React.createRef();
    this.uiKeyboardRef = React.createRef();
    this.controlPanelRef = React.createRef();
    this.toolbarRef = React.createRef();
  }

  componentDidMount() {
    this.loadSettings();
    this.initializeLarkSR();
  }

  componentDidUpdate(prevProps) {
    // Check if we need to reinitialize due to prop changes
    const authCodeChanged = prevProps.authCode !== this.props.authCode;
    const applicationIdChanged = prevProps.applicationId !== this.props.applicationId;

    // Only reinitialize if we have valid props and something meaningful changed
    if (this.props.authCode &&
        this.props.applicationId &&
        (authCodeChanged || applicationIdChanged)) {

      // Prevent reinitialization if already connecting or if we don't have a previous state to compare
      if (!this.state.isConnecting && (prevProps.authCode || prevProps.applicationId)) {
        console.log("Reinitializing LarkSR due to prop changes:", {
          authCodeChanged,
          applicationIdChanged,
          newAuthCode: this.props.authCode,
          newApplicationId: this.props.applicationId
        });

        this.reinitializeLarkSR();
      }
    }
  }

  loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('larksr_settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        this.setState({
          larkSRSettings: { ...this.state.larkSRSettings, ...parsedSettings }
        });
      }
    } catch (error) {
      console.warn('Failed to load LarkSR settings:', error);
    }
  };

  // Separate method for reinitialization with proper cleanup sequencing
  async reinitializeLarkSR() {
    // Set connecting state immediately to prevent multiple reinitializations
    this.setState({
      isConnecting: true,
      connectionError: null,
      sdkConnected: false,
      remoteReady: false
    });

    try {
      // Step 1: Cleanup existing instance
      await this.cleanup();

      // Step 2: Wait a bit to ensure complete cleanup
      await new Promise(resolve => setTimeout(resolve, 100));

      // Step 3: Clear any remaining DOM elements in the container
      if (this.myRef.current) {
        // Remove all child elements except our UI containers
        const children = Array.from(this.myRef.current.children);
        children.forEach(child => {
          if (child !== this.uiContainerRef.current &&
              child !== this.uiKeyboardRef.current &&
              !child.classList.contains('loading-overlay') &&
              !child.classList.contains('error-overlay')) {
            child.remove();
          }
        });
      }

      // Step 4: Initialize new instance
      this.initializeLarkSR();

    } catch (error) {
      console.error("Error during LarkSR reinitialization:", error);
      this.setState({
        connectionError: `Reinitialization failed: ${error.message}`,
        isConnecting: false,
        sdkConnected: false
      });
    }
  }

  initializeLarkSR() {
    console.log("Initializing LarkSR with settings...", this.state.larkSRSettings);

    // Validate DOM element is available
    if (!this.myRef.current) {
      const error = "Root element not available for LarkSR initialization";
      console.error(error);
      this.setState({
        connectionError: error,
        isConnecting: false
      });
      return;
    }

    // Validate required props
    const applicationId = this.props.applicationId;
    const authCode = this.props.authCode;

    if (!authCode || !applicationId) {
      const error = "Missing required props: authCode or applicationId";
      console.error(error);
      this.setState({
        connectionError: error,
        isConnecting: false
      });
      return;
    }

    // Validate auth code format (should be at least 8 characters)
    if (authCode.length < 8) {
      const error = `Invalid auth code format. Expected at least 8 characters, got ${authCode.length}`;
      console.error(error);
      this.setState({
        connectionError: error,
        isConnecting: false
      });
      return;
    }

    try {
      // Create new LarkSR instance with current settings
      const larksr = new LarkSR({
        rootElement: this.myRef.current,
        serverAddress: "https://ps.propvr.io:8181/",
        scaleMode: this.state.larkSRSettings.scaleMode,
        fullScreenMode: this.state.larkSRSettings.fullScreenMode,
        mobileFullScreenMode: this.state.larkSRSettings.mobileFullScreenMode,
        logLevel: this.state.larkSRSettings.logLevel,
        useSeparateMediaSharePeer: this.state.larkSRSettings.useSeparateMediaSharePeer,
      });

      const sdkId = "2b8edf14c2e348ab8211a742af4d5a15";

      console.log("LarkSR instance created, initializing SDK...", {
        applicationId,
        authCodeLength: authCode.length,
        sdkId,
        settings: this.state.larkSRSettings
      });

      // Initialize SDK and connect
      larksr.initSDKAuthCode(sdkId)
        .then(() => {
          console.log("Host: SDK auth code initialized successfully");
          return larksr.connect({
            appliId: applicationId,
            playerMode: 0,
            userType: 1,
            authCode: authCode
          });
        })
        .then(() => {
          console.log("Host: LarkSR connected successfully");
          this.setState({
            isConnecting: false,
            connectionError: null,
            sdkConnected: true
          });

          // Notify parent component of successful connection with SDK ID
          if (this.props.onConnectionSuccess) {
            this.props.onConnectionSuccess(sdkId);
          }
        })
        .catch((e) => {
          console.error("Host: LarkSR connection error:", e);
          console.error("Error details:", {
            message: e.message,
            code: e.code,
            authCode: authCode,
            applicationId: applicationId
          });

          const errorMessage = e.message || JSON.stringify(e);
          this.setState({
            connectionError: `Authentication failed: ${errorMessage}`,
            isConnecting: false,
            sdkConnected: false
          });

          // Notify parent component of connection error
          if (this.props.onConnectionError) {
            this.props.onConnectionError(e);
          }
        });

      this.setupEventListeners(larksr);
      this.larksr = larksr;

      if (Capabilities.isMobile && this.state.larkSRSettings.joystickEnabled) {
        this.initializeMobileControls();
      }

    } catch (error) {
      console.error("Error creating LarkSR instance:", error);
      this.setState({
        connectionError: `Failed to create LarkSR instance: ${error.message}`,
        isConnecting: false,
        sdkConnected: false
      });
    }
  }

  async cleanup() {
    console.log("Starting LarkSR cleanup...");

    try {
      // Cleanup mobile controls first
      if (this.joystick) {
        this.joystick.destroy();
        this.joystick = null;
      }
      if (this.keyboard) {
        this.keyboard.destroy();
        this.keyboard = null;
      }

      // Cleanup LarkSR instance
      if (this.larksr) {
        // Remove event listeners before destroying
        const events = [
          'connect', 'gotremotesteam', 'meidaloaded',
          'mediaplaysuccess', 'mediaplayfailed', 'meidaplaymute',
          'error', 'info', 'apprequestinput', 'resourcenotenough'
        ];

        events.forEach(event => {
          try {
            this.larksr.off(event);
          } catch (e) {
            console.warn(`Failed to remove ${event} listener:`, e);
          }
        });

        // Destroy the SDK instance
        try {
          this.larksr.destroy();
        } catch (e) {
          console.warn("Error destroying LarkSR instance:", e);
        }

        this.larksr = null;
      }

      // Clear any video/canvas elements that might be left behind
      if (this.myRef.current) {
        const videos = this.myRef.current.querySelectorAll('video');
        const canvases = this.myRef.current.querySelectorAll('canvas');

        videos.forEach(video => {
          try {
            video.pause();
            video.srcObject = null;
            video.src = '';
            video.load();
          } catch (e) {
            console.warn("Error cleaning up video element:", e);
          }
        });

        canvases.forEach(canvas => {
          try {
            const ctx = canvas.getContext('2d');
            if (ctx) {
              ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
          } catch (e) {
            console.warn("Error cleaning up canvas element:", e);
          }
        });
      }

      console.log("LarkSR cleanup completed successfully");

    } catch (error) {
      console.error("Error during LarkSR cleanup:", error);
      throw error;
    }
  }

  initializeMobileControls() {
    const { Joystick, Keyboard } = PxyWebCommonUI;

    if (this.state.larkSRSettings.joystickEnabled) {
      this.joystick = new Joystick({
        rootElement: this.uiContainerRef.current,
        larksr: this.larksr,
        subType: this.state.larkSRSettings.joystickSubType,
        position: { top: 150, left: 100 },
        size: { width: 150, height: 150 },
        centerSize: { width: 60, height: 60 },
      });
      this.joystick.hide();
    }

    if (this.state.larkSRSettings.keyboardEnabled) {
      this.keyboard = new Keyboard({
        rootElement: this.uiKeyboardRef.current,
        larksr: this.larksr,
        language: 'en'
      });
      this.keyboard.on('keyboardVal', (e) => {
        console.log('Virtual keyboard input:', e.detail);
      });
      this.keyboard.hide();
    }
  }

  setupEventListeners(larksr) {
    const events = [
      'connect', 'gotremotesteam', 'meidaloaded',
      'mediaplaysuccess', 'mediaplayfailed', 'meidaplaymute',
      'error', 'info', 'apprequestinput', 'resourcenotenough'
    ];

    events.forEach(event => {
      larksr.on(event, this.handleLarkEvent(event));
    });
  }

  handleLarkEvent = (eventName) => (e) => {
    console.log(`Host LarkSR ${eventName} Event:`, e);

    switch(eventName) {
      case 'connect':
        console.log("Host: LarkSR connected event received");
        this.setState({
          sdkConnected: true,
          isConnecting: false,
          connectionError: null
        });
        if (this.props.onConnectionSuccess) {
          const sdkId = "2b8edf14c2e348ab8211a742af4d5a15";
          this.props.onConnectionSuccess(sdkId);
        }
        break;
      case 'meidaloaded':
        console.log("Host: Media loaded successfully");
        this.setState({ remoteReady: true });
        break;
      case 'mediaplaysuccess':
        console.log("Host: Media play success");
        if (this.joystick && this.state.larkSRSettings.joystickEnabled) {
          this.joystick.show();
        }
        break;
      case 'error':
        console.error("Host: LarkSR error event:", e);
        const errorMessage = e.message || JSON.stringify(e);
        this.setState({
          connectionError: errorMessage,
          isConnecting: false,
          sdkConnected: false
        });
        if (this.props.onConnectionError) {
          this.props.onConnectionError(e);
        }
        break;
      case 'apprequestinput':
        console.log("App request input:", e);
        if (e.data === true) {
          if (Capabilities.isMobile && this.keyboard && this.state.larkSRSettings.keyboardEnabled) {
            this.keyboard.show();
          }
        } else {
          if (Capabilities.isMobile && this.keyboard) {
            this.keyboard.hide();
          }
        }
        break;
      case 'resourcenotenough':
        console.error("Host: Resource not enough");
        this.setState({
          connectionError: "Resource not enough - please try again later",
          isConnecting: false,
          sdkConnected: false
        });
        break;
    }
  }

  // Control Panel Event Handlers
  handleSettingsChange = (newSettings) => {
    console.log("Settings changed:", newSettings);
    this.setState({ larkSRSettings: newSettings });

    // Apply settings that require reconnection
    const reconnectRequired = [
      'scaleMode', 'fullScreenMode', 'mobileFullScreenMode',
      'logLevel', 'useSeparateMediaSharePeer'
    ];

    const needsReconnect = reconnectRequired.some(key =>
      this.state.larkSRSettings[key] !== newSettings[key]
    );

    if (needsReconnect && this.state.sdkConnected) {
      console.log("Settings require reconnection, reinitializing...");
      this.reinitializeLarkSR();
    }
  };

  handleOpenSettings = () => {
    this.setState({ showControlPanel: true });
  };

  handleCloseSettings = () => {
    this.setState({ showControlPanel: false });
  };

  // Toolbar Event Handlers
  handleFullscreen = () => {
    if (this.larksr && this.larksr.fullScreen) {
      this.larksr.fullScreen.requestFullScreen();
    } else if (this.myRef.current) {
      if (this.myRef.current.requestFullscreen) {
        this.myRef.current.requestFullscreen();
      } else if (this.myRef.current.webkitRequestFullscreen) {
        this.myRef.current.webkitRequestFullscreen();
      } else if (this.myRef.current.msRequestFullscreen) {
        this.myRef.current.msRequestFullscreen();
      }
    }
  };

  handleScreenshot = () => {
    if (this.larksr && this.larksr.screenshot) {
      this.larksr.screenshot()
        .then((blob) => {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `larksr-screenshot-${Date.now()}.png`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.error("Screenshot failed:", error);
        });
    }
  };

  handleDisconnect = () => {
    if (this.larksr) {
      this.cleanup();
      this.setState({
        sdkConnected: false,
        remoteReady: false,
        connectionError: null,
        isConnecting: false
      });
    }
  };

  handleReconnect = () => {
    this.setState({ isConnecting: true, connectionError: null });
    this.initializeLarkSR();
  };

  getConnectionStatus = () => {
    if (this.state.isConnecting) return 'connecting';
    if (this.state.connectionError) return 'error';
    if (this.state.sdkConnected) return 'connected';
    return 'disconnected';
  };

  render() {
    return (
      <div ref={this.myRef} style={{ position: 'relative', width: '100%', height: '100%' }}>
        {/* UI Containers for mobile controls */}
        <div ref={this.uiContainerRef}
          style={{
            position: 'absolute',
            zIndex: 2000
          }}
        />
        <div ref={this.uiKeyboardRef}
          style={{
            position: 'absolute',
            zIndex: 2000,
            bottom: 0,
            width: '100%'
          }}
        />

        {/* Control Panel */}
        {this.state.showControlPanel && (
          <LarkSRControlPanel
            ref={this.controlPanelRef}
            onSettingsChange={this.handleSettingsChange}
            onClose={this.handleCloseSettings}
            initialSettings={this.state.larkSRSettings}
          />
        )}

        {/* Toolbar */}
        {this.state.larkSRSettings.showControls && (
          <LarkSRToolbar
            ref={this.toolbarRef}
            larksr={this.larksr}
            connectionStatus={this.getConnectionStatus()}
            showNetworkStats={this.state.larkSRSettings.showNetworkStatus}
            onOpenSettings={this.handleOpenSettings}
            onFullscreen={this.handleFullscreen}
            onScreenshot={this.handleScreenshot}
            onDisconnect={this.handleDisconnect}
            onReconnect={this.handleReconnect}
          />
        )}

        {/* Loading overlay */}
        {this.state.isConnecting && (
          <div className="loading-overlay" style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-white text-xl font-semibold">Connecting to LarkXR...</p>
            <p className="text-gray-300 mt-2">Please wait while we establish the connection</p>
          </div>
        )}

        {/* Error overlay */}
        {this.state.connectionError && !this.state.isConnecting && (
          <div className="error-overlay" style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <p className="text-white text-xl font-semibold">Connection Error</p>
            <p className="text-gray-300 mt-2 text-center px-4">{this.state.connectionError}</p>
            <button
              onClick={() => {
                this.setState({ connectionError: null, isConnecting: true });
                this.initializeLarkSR();
              }}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry Connection
            </button>
          </div>
        )}
      </div>
    );
  }

  componentWillUnmount() {
    this.cleanup();
  }
}
