.container {
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(0,0,0,0.4);
    background-color: rgba(0,0,0,0.05);
    position: relative;
    /* overflow: hidden; */
}
/*
 body background
*/
.bgblack {
    background-color: black;
}
.bgwhite {
    background-color: #fff;
}
/* #video {
    position: absolute;
    z-index: -9999;
    top: -9999px;
    left: -9999px;
} */
/* alert */
.alert {
    display: flex;
    padding: 0 15px;
    justify-content: flex-start;
    align-items: center;
    background-color: hsla(0,0%,100%,.5);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 30px;
    z-index: 20;
    box-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}
.alertInfo {
    flex: 12;
    white-space: nowrap;
}

/* controlbar */
.controlBar {
    color: #fff;
    padding: 0 15px;
    display: flex;
    width: 100%;
    height: 30px;
    background-color: #333;
    position: absolute;
    bottom: 0;
    z-index: 10;
    /* overflow: auto; */
}
.toggleControlBarBtn {
    color: #fff;
    /* padding: 0 15px; */
    display: -ms-flexbox;
    display: flex;
    width: 30px;
    height: 30px;
    background-color: #333;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 10;
    border-radius: 5px;
    /* overflow: scroll; */
}
/* racker */
.pointer {
    display: flex;
    flex-direction: row;
    height: 1px;
    position: absolute;
}
.touchPoint {
    width: 40px;
    height: 40px;
    background-color: #32cdcd;
    border-radius: 20px;
    position: absolute;
    left: 0;
    top: 0;
}
/* loading */
.loading {
    text-align: center;
    /* animation: spin infinite 5s linear; */
    height: 100px;
    width: 100px;
    position: absolute;
    top: 50%;
    left: 50%;
    /* margin-left: -50px; */
    margin-top: -50px;
}

.loading img {
    height: 100px;
    width: 100px;
}

@keyframes spin {
from { transform: rotate(0deg); }
to { transform: rotate(360deg); }
}
  
/* popup */
.popup {
    width: 30px;
    height: 30px;
    position: absolute;
    top:50%;
    left: 0;
    z-index: 50;
}

.popup>img {
    display: block;
    width: 100%;
    height: auto;
}

.popup:hover {
    cursor: pointer;
}

.popupPanel {
    position: absolute;
    top: -144px;
    left: 0;
    width: 80px;
    height: 140px;
    background-color: #fff;
    border-radius: 3px;
    z-index: 51;
    border: 1px solid rgba(0,0,0,0.1);
}

/* button */
.button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    flex: 1;
    transition: all .1s;
    position: relative;
    height: 100%;
    border-radius: 3px;
    padding:4px;
}
.button .buttonTitle {
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
}

.button:hover {
    box-shadow: 0 0 10px  rgba(50, 205, 205,0.5);
}

.button>.buttonInfo {
    position: absolute;
    color: #fff;
    bottom: 30px;
    left: 0;
    width: 100%;
    background-color: rgba(0,0,0,.7);
    /* border-radius: 5px; */
    /* padding: 2px 5px; */
    font-size: 12px;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
}

.button>img {
    display: block;
    height: 100%;
    width: auto;
}

.buttonRow {
    display: flex;
    flex-direction: row;
    height: 100%;
    align-items: center;
}

.buttonRow>.buttonRowCell {
    display: flex;
    flex: 1;
    height: 100%;
    width: auto;
    margin: 2px;
}

/* net status */
.netStatus {
    display: flex;
    flex: 1;
    flex-direction: row;
    height: 100%;
    padding: 3px;
    justify-content: center;
    align-items: center;
}

.netStatus>img {
    width: auto;
    height: 100%;
    display: block;
    margin: 5px 10px;
}

.netStatusInfo {
    white-space: nowrap;
}

/* setting panel */ 
.settingPanel {
    position: absolute;
    z-index: 100;
    background-color: rgba(0,0,0,.3);
    display: flex;
    justify-content: center;
    align-items: center;
}

.settingPanelContainer {
    width: 70%;
    height: 80%;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    overflow: auto;
}

.settingCloseBtn {
    height: 35px;
    width: 35px;
    float: right;
}
/* */
.toast {
    position: absolute;
    width: 100%;
    text-align: center;
    /* bottom: 40px; */
    top: 50%;
}
.toastText {
    background-color: rgba(0,0,0,0.7);
    border-radius: 20px;
    /* line-height: 37px; */
    padding: 5px 20px;
    color: white;
    font-size: 25px;
}
/* */
.modal-title {
    text-align: center;
    font-weight: 600;
    margin-bottom: 1rem;
}
.modal-session-title {
    margin-bottom: 1rem;
    font-weight: 600;
}
.modal-item {
    margin-bottom: 1rem;
}
.modal-item-p {
    margin: 0; 
}
/* */
.toggleList {
    display: flex;
}
/* other */
.block {
    flex: 10;
    display: flex;
}

.relativeNil {
    position: relative;
}

@media screen and (max-width:620px) {
    .controlBar {
        overflow: auto;
    }
}

@media screen and (min-width:768px) {
    .block {
        flex: 15;
    }
}

@media screen and (min-width:1200px) {
    .block {
        flex: 30;
    }
}

/* common */
.flex {
    display: flex;
}
.absolute {
    position: absolute
}
.clearfix::after {
    display: block;
    content: "";
    clear: both;
}
.btnRecommend {
    display: block;
    color: #fff !important;
    background-color: darkturquoise;
    text-align: center;
    line-height: 35px;
    border-radius: 5px;
    margin-bottom: 15px;
}
.btnUnrecommend {
    display: block;
    color: #fff;
    background-color: darkgrey;
    text-align: center;
    line-height: 35px;
    border-radius: 5px;
    margin-bottom: 15px;
}
/*
*/
.modalWechat {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.35);
    top:0;
    left:0;
    z-index: 200;
    /* display: none; */
}
.modalWechatArr {
    position: fixed;
    right: 0;
    width: 100px;
}

.modalWechatInfo {
    color: #fff;
    font-size: 22px;
    /* top: 50px; */
    position: absolute;
    top: 90px;
    width: 100%;
    text-align: center;
    padding: 0;
    margin: 0;
    line-height: 1;
}

/* */
.alertModalContainer {
    width: 230px;
    /* height: 100px; */
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    overflow: auto;
    word-break: break-word;
}

.alertModalBtnContainer .button{
    background-color: #32cdcd;
    color: #fff;
}

/*  */
.localRenderCursor {
    position: absolute;
    top: 0;
    left: 0;
    width: 24px;
    height: 24px;
}