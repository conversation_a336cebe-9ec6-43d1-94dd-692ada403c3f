import React, { Component } from 'react';
import './LarkSRToolbar.css';

export default class LarkSRToolbar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVisible: true,
      isCollapsed: false,
      networkStats: {
        ping: 0,
        fps: 0,
        bitrate: 0,
        packetLoss: 0,
      },
      connectionStatus: 'disconnected', // disconnected, connecting, connected, error
    };
  }

  componentDidMount() {
    // Start network monitoring if connected
    if (this.props.larksr && this.props.connectionStatus === 'connected') {
      this.startNetworkMonitoring();
    }
  }

  componentDidUpdate(prevProps) {
    // Start/stop network monitoring based on connection status
    if (prevProps.connectionStatus !== this.props.connectionStatus) {
      if (this.props.connectionStatus === 'connected') {
        this.startNetworkMonitoring();
      } else {
        this.stopNetworkMonitoring();
      }
    }
  }

  componentWillUnmount() {
    this.stopNetworkMonitoring();
  }

  startNetworkMonitoring = () => {
    // Mock network stats - in real implementation, get from LarkSR SDK
    this.networkInterval = setInterval(() => {
      if (this.props.larksr && this.props.connectionStatus === 'connected') {
        // In real implementation, get actual stats from LarkSR SDK
        this.setState({
          networkStats: {
            ping: Math.floor(Math.random() * 50) + 20, // 20-70ms
            fps: Math.floor(Math.random() * 10) + 55, // 55-65fps
            bitrate: Math.floor(Math.random() * 5000) + 15000, // 15-20 Mbps
            packetLoss: Math.random() * 2, // 0-2%
          }
        });
      }
    }, 1000);
  };

  stopNetworkMonitoring = () => {
    if (this.networkInterval) {
      clearInterval(this.networkInterval);
      this.networkInterval = null;
    }
  };

  toggleToolbar = () => {
    this.setState({ isCollapsed: !this.state.isCollapsed });
  };

  handleFullscreen = () => {
    if (this.props.onFullscreen) {
      this.props.onFullscreen();
    }
  };

  handleScreenshot = () => {
    if (this.props.onScreenshot) {
      this.props.onScreenshot();
    }
  };

  handleDisconnect = () => {
    if (this.props.onDisconnect) {
      this.props.onDisconnect();
    }
  };

  handleReconnect = () => {
    if (this.props.onReconnect) {
      this.props.onReconnect();
    }
  };

  getConnectionStatusColor = () => {
    switch (this.props.connectionStatus) {
      case 'connected':
        return '#28a745';
      case 'connecting':
        return '#ffc107';
      case 'error':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  getConnectionStatusText = () => {
    switch (this.props.connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Connection Error';
      default:
        return 'Disconnected';
    }
  };

  renderNetworkStats = () => {
    if (this.props.connectionStatus !== 'connected' || !this.props.showNetworkStats) {
      return null;
    }

    const { networkStats } = this.state;
    
    return (
      <div className="network-stats">
        <div className="stat-item">
          <span className="stat-label">Ping:</span>
          <span className="stat-value">{networkStats.ping}ms</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">FPS:</span>
          <span className="stat-value">{networkStats.fps}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Bitrate:</span>
          <span className="stat-value">{(networkStats.bitrate / 1000).toFixed(1)}Mbps</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Loss:</span>
          <span className="stat-value">{networkStats.packetLoss.toFixed(1)}%</span>
        </div>
      </div>
    );
  };

  renderConnectionStatus = () => (
    <div className="connection-status">
      <div 
        className="status-indicator"
        style={{ backgroundColor: this.getConnectionStatusColor() }}
      />
      <span className="status-text">{this.getConnectionStatusText()}</span>
    </div>
  );

  renderControlButtons = () => (
    <div className="control-buttons">
      {this.props.connectionStatus === 'connected' && (
        <>
          <button
            className="toolbar-button"
            onClick={this.handleFullscreen}
            title="Toggle Fullscreen"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
            </svg>
          </button>
          
          <button
            className="toolbar-button"
            onClick={this.handleScreenshot}
            title="Take Screenshot"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5zM17 3H7c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
            </svg>
          </button>
          
          <button
            className="toolbar-button disconnect"
            onClick={this.handleDisconnect}
            title="Disconnect"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13 3h-2v10h2V3zm4.83 2.17l-1.42 1.42C17.99 7.86 19 9.81 19 12c0 3.87-3.13 7-7 7s-7-3.13-7-7c0-2.19 1.01-4.14 2.58-5.42L6.17 5.17C4.23 6.82 3 9.26 3 12c0 4.97 4.03 9 9 9s9-4.03 9-9c0-2.74-1.23-5.18-3.17-6.83z"/>
            </svg>
          </button>
        </>
      )}
      
      {(this.props.connectionStatus === 'disconnected' || this.props.connectionStatus === 'error') && (
        <button
          className="toolbar-button reconnect"
          onClick={this.handleReconnect}
          title="Reconnect"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 6v3l4-4-4-4v3c-4.42 0-8 3.58-8 8 0 1.57.46 3.03 1.24 4.26L6.7 14.8c-.45-.83-.7-1.79-.7-2.8 0-3.31 2.69-6 6-6zm6.76 1.74L17.3 9.2c.44.84.7 1.79.7 2.8 0 3.31-2.69 6-6 6v-3l-4 4 4 4v-3c4.42 0 8-3.58 8-8 0-1.57-.46-3.03-1.24-4.26z"/>
          </svg>
        </button>
      )}
      
      <button
        className="toolbar-button settings"
        onClick={this.props.onOpenSettings}
        title="Open Settings"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
        </svg>
      </button>
    </div>
  );

  render() {
    if (!this.state.isVisible) {
      return null;
    }

    return (
      <div className={`larksr-toolbar ${this.state.isCollapsed ? 'collapsed' : ''}`}>
        <div className="toolbar-content">
          <div className="toolbar-left">
            {this.renderConnectionStatus()}
            {!this.state.isCollapsed && this.renderNetworkStats()}
          </div>
          
          <div className="toolbar-right">
            {!this.state.isCollapsed && this.renderControlButtons()}
            <button
              className="toolbar-button toggle"
              onClick={this.toggleToolbar}
              title={this.state.isCollapsed ? "Expand Toolbar" : "Collapse Toolbar"}
            >
              <svg 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="currentColor"
                style={{ 
                  transform: this.state.isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.2s'
                }}
              >
                <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    );
  }
}
