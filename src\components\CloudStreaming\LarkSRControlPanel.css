/* LarkSR Control Panel Styles */
.larksr-control-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(2px);
}

.larksr-control-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.settings-section {
  margin-bottom: 30px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 20px;
}

.settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.settings-section h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.2rem;
  font-weight: 600;
  border-left: 4px solid #667eea;
  padding-left: 12px;
}

.setting-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.setting-item label {
  font-weight: 500;
  color: #555;
  min-width: 150px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-item select,
.setting-item input[type="number"] {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.setting-item select:focus,
.setting-item input[type="number"]:focus {
  outline: none;
  border-color: #667eea;
}

.setting-item input[type="range"] {
  flex: 1;
  margin: 0 10px;
}

.setting-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
}

.setting-item span {
  min-width: 40px;
  text-align: right;
  font-weight: 600;
  color: #667eea;
}

.panel-footer {
  background: #f8f9fa;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  border-top: 1px solid #e0e0e0;
}

.reset-button,
.close-button-footer {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.reset-button {
  background: #dc3545;
  color: white;
}

.reset-button:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.close-button-footer {
  background: #667eea;
  color: white;
}

.close-button-footer:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .larksr-control-panel {
    width: 95%;
    max-height: 90vh;
  }
  
  .panel-header {
    padding: 15px;
  }
  
  .panel-header h2 {
    font-size: 1.3rem;
  }
  
  .panel-content {
    padding: 15px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .setting-item label {
    min-width: auto;
    justify-content: flex-start;
  }
  
  .panel-footer {
    padding: 15px;
    flex-direction: column;
  }
  
  .reset-button,
  .close-button-footer {
    width: 100%;
  }
}

/* Scrollbar Styling */
.panel-content::-webkit-scrollbar {
  width: 8px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation */
.larksr-control-panel-overlay {
  animation: fadeIn 0.3s ease-out;
}

.larksr-control-panel {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Disabled state */
.setting-item select:disabled,
.setting-item input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* Focus states for accessibility */
.close-button:focus,
.reset-button:focus,
.close-button-footer:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}
