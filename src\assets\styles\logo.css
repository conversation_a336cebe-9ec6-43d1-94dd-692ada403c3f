@keyframes fastspin {
    0% {
        transform: rotateX(-33.5deg) rotateY(45deg)
    }
    50%,to {
        transform: rotateX(-33.5deg) rotateY(-315deg)
    }
}
.logo-cube {
    /* position: absolute;*/
    /* left: 50%; */
    margin-left: -50px;
    transform: rotateX(-33.5deg) rotateY(45deg);
    transform-origin: 50px 50px;
    transform-style: preserve-3d;
    animation: fastspin 1.8s ease-in-out infinite 0.1s;
}
.logo-cube .side {
    position: absolute;
    width: 100px;
    height: 100px;
    transition: transform 1s ease-in-out;
    background: rgba(51,204,204,0.15);
    border-radius: 15px 0 0 0;
    overflow: hidden;
}
.logo-cube .bottom {
    transform: rotateX(-90deg) translateZ(50px);
}
.logo-cube .left {
    transform: rotateX(0deg) translateZ(-50px);
}
.logo-cube .back {
    transform: rotateY(90deg) translateZ(50px);
}
.logo-cube .topBar {
    background: #32cdcd;
    height: 25px;
    width: 100px;
}
.logo-cube .bottom-side-top {
    height: 20px;
}
.logo-cube .bottom-side-top-left {
    background: #32cdcd;
    float: left;
    height: 20px;
    width: 75px;
    border-radius: 0 0 15px 0;
}
.logo-cube .bottom-side-top-right {
    background: #32cdcd;
    float: right;
    height: 20px;
    width: 20px;
    border-radius: 15px 0 0 0;
}
.logo-cube .bottom-side-body {
    width: 100px;
    height: 55px;
}
.logo-cube .bottom-side-body-left {
    width: 20px;
    height: 55px;
    background: #32cdcd;
    float: left;
}
.logo-cube .bottom-side-body-right {
    width: 20px;
    height: 55px;
    background: #32cdcd;
    float: right;
}
.logo-cube .bottom-side-bottom {
    background: #32cdcd;
    height: 20px;
    width: 100px;
}